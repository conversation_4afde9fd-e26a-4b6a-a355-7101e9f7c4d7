#!/usr/bin/env python3
"""
Writing Tools - Unified Settings Test Suite
Tests the new unified settings system to ensure proper functionality
"""

import json
import logging
import os
import sys
import tempfile
import shutil
from pathlib import Path

# Enable debug logging
logging.basicConfig(level=logging.DEBUG, format="%(levelname)s: %(message)s")

# Add the parent directory to the path to import config modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import SettingsManager
from config.interfaces import SystemConfig, ActionConfig, UnifiedSettings
from config.constants import DEFAULT_SETTINGS, DEFAULT_SYSTEM, DEFAULT_ACTIONS


class TestUnifiedSettings:
    """Test suite for the unified settings system"""

    def __init__(self):
        self.test_dir = None
        self.original_cwd = os.getcwd()
        self.passed_tests = 0
        self.total_tests = 0

    def setup_test_environment(self):
        """Create a temporary test environment"""
        self.test_dir = tempfile.mkdtemp(prefix="writing_tools_test_")
        os.chdir(self.test_dir)
        print(f"Test environment: {self.test_dir}")

    def cleanup_test_environment(self):
        """Clean up the test environment"""
        os.chdir(self.original_cwd)
        if self.test_dir and os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def assert_test(self, condition, test_name, error_msg=""):
        """Assert a test condition and track results"""
        self.total_tests += 1
        if condition:
            print(f"✓ {test_name}")
            self.passed_tests += 1
        else:
            print(f"✗ {test_name}: {error_msg}")

    def test_default_settings_loading(self):
        """Test loading default settings when no files exist"""
        print("\n--- Test: Default Settings Loading ---")

        settings_manager = SettingsManager(mode="dev")
        settings = settings_manager.load_settings()

        # Test system settings
        self.assert_test(
            settings.system.provider == "Gemini", "Default provider is Gemini"
        )
        self.assert_test(
            settings.system.hotkey == "ctrl+space", "Default hotkey is ctrl+space"
        )
        self.assert_test(settings.system.api_key == "", "Default API key is empty")

        # Test actions
        self.assert_test(
            "Proofread" in settings.actions, "Proofread action exists in defaults"
        )
        self.assert_test(
            "Rewrite" in settings.actions, "Rewrite action exists in defaults"
        )
        self.assert_test(
            settings.actions["Summary"].open_in_window == True,
            "Summary action opens in window",
        )

    def test_settings_persistence(self):
        """Test saving and loading settings"""
        print("\n--- Test: Settings Persistence ---")

        # Create and save settings
        test_file = os.path.join(os.getcwd(), "test_data.json")
        settings_manager = SettingsManager(test_file)
        settings_manager.load_settings()

        # Modify settings
        settings_manager.update_system_setting("api_key", "test_key_123")
        settings_manager.update_system_setting("provider", "OpenAI")

        # Verify file exists
        self.assert_test(os.path.exists(test_file), "Data file was created")

        # Create new manager and load
        new_manager = SettingsManager(test_file)
        new_settings = new_manager.load_settings()

        self.assert_test(
            new_settings.system.api_key == "test_key_123", "API key persisted correctly"
        )
        self.assert_test(
            new_settings.system.provider == "OpenAI", "Provider persisted correctly"
        )

    def test_legacy_migration(self):
        """Test migration from legacy config.json and options.json"""
        print("\n--- Test: Legacy Migration ---")

        # Create legacy config.json
        legacy_config = {
            "api_key": "legacy_api_key",
            "provider": "Ollama",
            "shortcut": "ctrl+alt+w",
            "locale": "fr",
        }
        with open("config.json", "w") as f:
            json.dump(legacy_config, f)

        # Create legacy options.json
        legacy_options = {
            "Custom Action": {
                "prefix": "Custom prefix:\n\n",
                "instruction": "Custom instruction",
                "icon": "icons/custom",
                "open_in_window": True,
            }
        }
        with open("options.json", "w") as f:
            json.dump(legacy_options, f)

        # Test migration
        settings_manager = SettingsManager("migrated_data.json")
        success = settings_manager.migrate_legacy_files("config.json", "options.json")

        self.assert_test(success, "Migration completed successfully")

        # Load migrated settings
        settings = settings_manager.load_settings()

        self.assert_test(
            settings.system.api_key == "legacy_api_key", "Legacy API key migrated"
        )
        self.assert_test(
            settings.system.provider == "Ollama", "Legacy provider migrated"
        )
        self.assert_test(
            settings.system.hotkey == "ctrl+alt+w", "Legacy shortcut migrated as hotkey"
        )
        self.assert_test(
            settings.system.language == "fr", "Legacy locale migrated as language"
        )
        self.assert_test(
            "Custom Action" in settings.actions, "Legacy custom action migrated"
        )
        self.assert_test(
            settings.actions["Custom Action"].open_in_window == True,
            "Legacy action properties migrated",
        )

    def test_intelligent_merging(self):
        """Test intelligent merging of user settings with defaults"""
        print("\n--- Test: Intelligent Merging ---")

        # Create partial user data (missing some defaults)
        user_data = {
            "system": {
                "api_key": "user_key",
                "provider": "Anthropic",
                # Missing hotkey, theme, etc.
            },
            "actions": {
                "Proofread": {
                    "prefix": "Custom proofread prefix:\n\n"
                    # Missing instruction, icon, etc.
                }
                # Missing other default actions
            },
        }

        test_file = os.path.join(os.getcwd(), "partial_data.json")
        with open(test_file, "w") as f:
            json.dump(user_data, f)

        # Debug: check file content and path
        print(f"DEBUG: Test file path: {test_file}")
        print(f"DEBUG: File exists: {os.path.exists(test_file)}")
        with open(test_file, "r") as f:
            file_content = f.read()
            print(f"DEBUG: File content: {file_content}")

        settings_manager = SettingsManager(test_file)
        settings = settings_manager.load_settings()

        # Debug prints
        print(f"DEBUG: Loaded API key: '{settings.system.api_key}'")
        print(f"DEBUG: Loaded provider: '{settings.system.provider}'")

        # Test that user values are preserved
        self.assert_test(
            settings.system.api_key == "user_key",
            f"User API key preserved (got: '{settings.system.api_key}')",
        )
        self.assert_test(
            settings.system.provider == "Anthropic",
            f"User provider preserved (got: '{settings.system.provider}')",
        )

        # Test that defaults fill in missing values
        self.assert_test(
            settings.system.hotkey == "ctrl+space", "Default hotkey filled in"
        )
        self.assert_test(settings.system.theme == "gradient", "Default theme filled in")

        # Test action merging
        print(f"DEBUG: Proofread prefix: '{settings.actions['Proofread'].prefix}'")
        self.assert_test(
            settings.actions["Proofread"].prefix == "Custom proofread prefix:\n\n",
            f"User action prefix preserved (got: '{settings.actions['Proofread'].prefix}')",
        )
        self.assert_test(
            settings.actions["Proofread"].icon == "icons/magnifying-glass",
            "Default action icon filled in",
        )
        self.assert_test("Rewrite" in settings.actions, "Default actions preserved")

    def test_action_management(self):
        """Test adding, updating, and removing actions"""
        print("\n--- Test: Action Management ---")

        test_file = os.path.join(os.getcwd(), "action_test.json")
        settings_manager = SettingsManager(test_file)
        settings_manager.load_settings()

        # Add new action
        new_action = ActionConfig(
            prefix="Test prefix:\n\n",
            instruction="Test instruction",
            icon="icons/test",
            open_in_window=False,
        )
        success = settings_manager.update_action("Test Action", new_action)

        self.assert_test(success, "New action added successfully")

        # Verify action was added
        settings = settings_manager.settings
        self.assert_test(
            "Test Action" in settings.actions, "New action exists in settings"
        )
        self.assert_test(
            settings.actions["Test Action"].prefix == "Test prefix:\n\n",
            "New action properties correct",
        )

        # Remove action
        success = settings_manager.remove_action("Test Action")
        self.assert_test(success, "Action removed successfully")

        # Verify action was removed
        self.assert_test(
            "Test Action" not in settings.actions, "Action no longer exists in settings"
        )

    def run_all_tests(self):
        """Run all tests"""
        print("=== Writing Tools Unified Settings Test Suite ===")

        try:
            self.setup_test_environment()

            self.test_default_settings_loading()
            self.test_settings_persistence()
            self.test_legacy_migration()
            self.test_intelligent_merging()
            self.test_action_management()

            print(f"\n=== Test Results ===")
            print(f"Passed: {self.passed_tests}/{self.total_tests}")

            if self.passed_tests == self.total_tests:
                print("✓ All tests passed!")
                return True
            else:
                print("✗ Some tests failed!")
                return False

        finally:
            self.cleanup_test_environment()


def main():
    """Main entry point"""
    test_suite = TestUnifiedSettings()
    success = test_suite.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

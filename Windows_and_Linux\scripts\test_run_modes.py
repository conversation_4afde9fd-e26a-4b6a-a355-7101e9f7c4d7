#!/usr/bin/env python3
"""
Writing Tools - Run Mode Testing Script
Tests all scenarios for the new run_mode system
"""

import json
import os
import shutil
import sys
import tempfile
from pathlib import Path

# Add the parent directory to the path to import config modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from config.settings import SettingsManager


class RunModeTestSuite:
    """Test suite for run mode functionality"""

    def __init__(self):
        self.test_dir = None
        self.original_cwd = os.getcwd()
        self.tests_passed = 0
        self.tests_failed = 0

    def setup_test_environment(self):
        """Create a temporary test environment"""
        self.test_dir = tempfile.mkdtemp(prefix="writing_tools_test_")
        os.chdir(self.test_dir)
        print(f"Test environment: {self.test_dir}")

        # Create basic directory structure
        os.makedirs("config", exist_ok=True)
        os.makedirs("config/config_sauv", exist_ok=True)

        # Create a minimal default_data.json
        default_data = {
            "system": {
                "api_key": "",
                "provider": "Gemini",
                "model": "gemini-2.0-flash",
                "run_mode": "dev",
            },
            "actions": {
                "Proofread": {
                    "prefix": "Proofread this:\n\n",
                    "instruction": "Test instruction",
                    "icon": "icons/magnifying-glass",
                    "open_in_window": False,
                }
            },
            "custom_data": {},
        }

        with open("config/default_data.json", "w") as f:
            json.dump(default_data, f, indent=2)

    def cleanup_test_environment(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        if self.test_dir and os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def assert_test(self, condition, test_name):
        """Assert a test condition"""
        if condition:
            print(f"✓ {test_name}")
            self.tests_passed += 1
        else:
            print(f"✗ {test_name}")
            self.tests_failed += 1

    def test_auto_mode_detection(self):
        """Test automatic mode detection"""
        print("\n--- Test: Auto Mode Detection ---")

        # Test script mode (should default to dev)
        sm_auto = SettingsManager()
        self.assert_test(
            sm_auto.mode == "dev", "Auto-detection defaults to 'dev' for script mode"
        )

        # Test explicit mode override
        sm_explicit = SettingsManager(mode="build-final")
        self.assert_test(
            sm_explicit.mode == "build-final", "Explicit mode override works"
        )

    def test_dev_mode_behavior(self):
        """Test dev mode behavior"""
        print("\n--- Test: Dev Mode Behavior ---")

        # Temporarily change sys.argv[0] to simulate running from test directory
        original_argv0 = sys.argv[0]
        sys.argv[0] = os.path.join(self.test_dir, "main.py")

        try:
            sm = SettingsManager(mode="dev")
            settings = sm.load_settings()

            # Check that data file path is in config_sauv
            expected_path = Path(self.test_dir) / "config" / "config_sauv" / "data.json"
            self.assert_test(
                sm.data_file == expected_path,
                "Dev mode uses config_sauv/data.json path",
            )

            # Check run_mode is set correctly
            self.assert_test(
                settings.system.run_mode == "dev", "Dev mode sets run_mode to 'dev'"
            )
        finally:
            sys.argv[0] = original_argv0

    def test_build_final_fallback(self):
        """Test build-final mode with fallback logic"""
        print("\n--- Test: Build-Final Fallback Logic ---")

        # Test 1: No data.json in root, no config_sauv - should use root
        sm1 = SettingsManager(mode="build-final")
        expected_root_path = Path(self.test_dir) / "data.json"
        self.assert_test(
            sm1.data_file == expected_root_path,
            "Build-final with no existing files uses root path",
        )

        # Test 2: Create config_sauv/data.json, should fallback to it
        config_sauv_data = {
            "system": {"provider": "Ollama", "run_mode": "dev"},
            "actions": {},
            "custom_data": {
                "providers": {"Ollama": {"api_base": "http://localhost:11434"}}
            },
        }

        with open("config/config_sauv/data.json", "w") as f:
            json.dump(config_sauv_data, f)

        sm2 = SettingsManager(mode="build-final")
        expected_fallback_path = (
            Path(self.test_dir) / "config" / "config_sauv" / "data.json"
        )
        self.assert_test(
            sm2.data_file == expected_fallback_path,
            "Build-final falls back to config_sauv when root data.json doesn't exist",
        )

        # Test 3: Create root data.json, should prefer it over config_sauv
        root_data = {
            "system": {"provider": "Gemini", "run_mode": "build_final"},
            "actions": {},
            "custom_data": {},
        }

        with open("data.json", "w") as f:
            json.dump(root_data, f)

        sm3 = SettingsManager(mode="build-final")
        self.assert_test(
            sm3.data_file == expected_root_path,
            "Build-final prefers root data.json over config_sauv",
        )

    def test_providers_detection(self):
        """Test provider configuration detection"""
        print("\n--- Test: Provider Configuration Detection ---")

        # Test 1: No providers configured
        sm1 = SettingsManager(mode="dev")
        settings1 = sm1.load_settings()
        self.assert_test(
            not sm1.has_providers_configured(),
            "Detects when no providers are configured",
        )

        # Test 2: Providers configured
        settings1.custom_data["providers"] = {
            "Gemini (Recommended)": {
                "api_key": "test_key",
                "model_name": "gemini-2.0-flash",
            }
        }
        sm1.save_settings()

        sm2 = SettingsManager(mode="dev")
        sm2.load_settings()
        self.assert_test(
            sm2.has_providers_configured(), "Detects when providers are configured"
        )

    def test_run_mode_persistence(self):
        """Test that run_mode is correctly saved and loaded"""
        print("\n--- Test: Run Mode Persistence ---")

        # Create settings with different modes
        for mode in ["dev", "build_dev", "build_final"]:
            sm = SettingsManager(mode=mode)
            settings = sm.load_settings()
            sm.save_settings()

            # Reload and check
            sm2 = SettingsManager(mode=mode)
            settings2 = sm2.load_settings()

            self.assert_test(
                settings2.system.run_mode == mode,
                f"Run mode '{mode}' is correctly persisted",
            )

    def run_all_tests(self):
        """Run all tests"""
        print("===== Writing Tools - Run Mode Test Suite =====")

        try:
            self.setup_test_environment()

            self.test_auto_mode_detection()
            self.test_dev_mode_behavior()
            self.test_build_final_fallback()
            self.test_providers_detection()
            self.test_run_mode_persistence()

            print(f"\n=== Test Results ===")
            print(f"Passed: {self.tests_passed}")
            print(f"Failed: {self.tests_failed}")

            if self.tests_failed == 0:
                print("🎉 All tests passed!")
                return True
            else:
                print("❌ Some tests failed!")
                return False

        except Exception as e:
            print(f"\n✗ Test suite failed with error: {e}")
            return False

        finally:
            self.cleanup_test_environment()


if __name__ == "__main__":
    test_suite = RunModeTestSuite()
    success = test_suite.run_all_tests()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Simple test for the new config system
"""

import json
import os
import sys
import tempfile
import shutil

# Add the parent directory to the path to import config modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import SettingsManager


def test_basic_functionality():
    """Test basic functionality of the new config system"""
    print("=== Simple Config System Test ===")
    
    # Create temp dir
    test_dir = tempfile.mkdtemp(prefix="writing_tools_simple_test_")
    old_cwd = os.getcwd()
    
    try:
        os.chdir(test_dir)
        
        # Create structure
        os.makedirs("config/config_sauv", exist_ok=True)
        os.makedirs("config/backgrounds", exist_ok=True)
        
        # Create default_data.json
        default_data = {
            "system": {
                "api_key": "",
                "provider": "Gemini",
                "model": "gemini-2.0-flash",
                "hotkey": "ctrl+space"
            },
            "actions": {
                "Proofread": {
                    "prefix": "Proofread this:\n\n",
                    "instruction": "Test instruction",
                    "icon": "icons/test",
                    "open_in_window": False
                }
            },
            "custom_data": {}
        }
        
        with open("config/default_data.json", "w") as f:
            json.dump(default_data, f, indent=2)
        
        print(f"Test environment: {test_dir}")
        
        # Test 1: Dev mode with defaults
        print("\n1. Testing dev mode with defaults...")
        sm1 = SettingsManager(mode="dev")
        settings1 = sm1.load_settings()
        
        assert settings1.system.provider == "Gemini", f"Expected Gemini, got {settings1.system.provider}"
        assert "Proofread" in settings1.actions, "Proofread action not found"
        print("✓ Dev mode defaults work")
        
        # Test 2: Save and reload
        print("\n2. Testing save and reload...")
        sm1.update_system_setting("api_key", "test_key_123")
        
        sm2 = SettingsManager(mode="dev")
        settings2 = sm2.load_settings()
        
        assert settings2.system.api_key == "test_key_123", f"Expected test_key_123, got {settings2.system.api_key}"
        print("✓ Save and reload works")
        
        # Test 3: Build-final mode
        print("\n3. Testing build-final mode...")
        # Create data.json in root
        with open("data.json", "w") as f:
            json.dump(default_data, f)
        
        sm3 = SettingsManager(mode="build-final")
        settings3 = sm3.load_settings()
        
        assert settings3.system.provider == "Gemini", f"Expected Gemini, got {settings3.system.provider}"
        assert str(sm3.data_file).endswith("data.json"), f"Expected data.json, got {sm3.data_file}"
        print("✓ Build-final mode works")
        
        print("\n=== All tests passed! ===")
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        return False
        
    finally:
        os.chdir(old_cwd)
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)


if __name__ == "__main__":
    success = test_basic_functionality()
    sys.exit(0 if success else 1)

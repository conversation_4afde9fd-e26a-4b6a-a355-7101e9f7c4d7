#!/usr/bin/env python3
"""
Writing Tools - Test Onboarding Logic
Tests that onboarding is shown when no providers are configured
"""

import os
import sys
import tempfile
import shutil
import json
import logging
from pathlib import Path

# Enable debug logging
logging.basicConfig(level=logging.DEBUG, format="%(levelname)s: %(message)s")

# Add the parent directory to the path to import config modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import SettingsManager


def test_has_providers_configured():
    """Test the has_providers_configured logic"""
    print("\n=== Testing has_providers_configured logic ===")
    
    # Create a temporary directory structure
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Using temp directory: {temp_dir}")
        
        # Create structure
        os.makedirs(f"{temp_dir}/config", exist_ok=True)
        
        # Test 1: Empty provider and api_key (should return False)
        print("\n--- Test 1: Empty provider and api_key ---")
        default_data = {
            "system": {
                "api_key": "",
                "provider": "",
                "model": "",
                "hotkey": "ctrl+space"
            },
            "actions": {},
            "custom_data": {}
        }
        
        with open(f"{temp_dir}/config/default_data.json", "w") as f:
            json.dump(default_data, f, indent=2)
        
        with open(f"{temp_dir}/data.json", "w") as f:
            json.dump(default_data, f, indent=2)
        
        original_cwd = os.getcwd()
        try:
            os.chdir(temp_dir)
            
            # Mock sys.frozen and sys.executable
            import sys
            original_frozen = getattr(sys, 'frozen', False)
            original_executable = getattr(sys, 'executable', '')
            
            sys.frozen = True
            sys.executable = f"{temp_dir}/Writing Tools.exe"
            
            settings_manager = SettingsManager(mode="build-final")
            settings_manager.load_settings()
            
            has_providers = settings_manager.has_providers_configured()
            print(f"has_providers_configured(): {has_providers}")
            print(f"provider: '{settings_manager.settings.system.provider}'")
            print(f"api_key: '{settings_manager.settings.system.api_key}'")
            print(f"custom_data: {settings_manager.settings.custom_data}")
            
            assert not has_providers, f"Expected False, got {has_providers}"
            print("✓ Correctly returns False for empty provider/api_key")
            
            # Test 2: Provider set but no api_key (should return False)
            print("\n--- Test 2: Provider set but no api_key ---")
            settings_manager.settings.system.provider = "Gemini"
            has_providers = settings_manager.has_providers_configured()
            print(f"has_providers_configured(): {has_providers}")
            assert not has_providers, f"Expected False, got {has_providers}"
            print("✓ Correctly returns False for provider without api_key")
            
            # Test 3: Both provider and api_key set (should return True)
            print("\n--- Test 3: Both provider and api_key set ---")
            settings_manager.settings.system.api_key = "test_key"
            has_providers = settings_manager.has_providers_configured()
            print(f"has_providers_configured(): {has_providers}")
            assert has_providers, f"Expected True, got {has_providers}"
            print("✓ Correctly returns True for provider with api_key")
            
            # Test 4: Custom providers in custom_data (should return True)
            print("\n--- Test 4: Custom providers in custom_data ---")
            settings_manager.settings.system.provider = ""
            settings_manager.settings.system.api_key = ""
            settings_manager.settings.custom_data = {
                "providers": {
                    "Gemini": {"api_key": "test_key", "model": "gemini-2.0-flash"}
                }
            }
            has_providers = settings_manager.has_providers_configured()
            print(f"has_providers_configured(): {has_providers}")
            assert has_providers, f"Expected True, got {has_providers}"
            print("✓ Correctly returns True for custom_data providers")
            
            # Restore original values
            sys.frozen = original_frozen
            sys.executable = original_executable
            
        finally:
            os.chdir(original_cwd)


def test_real_dist_data():
    """Test the actual dist/data.json file"""
    print("\n=== Testing real dist/data.json ===")
    
    dist_data_path = Path(__file__).parent.parent / "dist" / "data.json"
    if not dist_data_path.exists():
        print("⚠ dist/data.json not found, skipping test")
        return
    
    # Change to dist directory and test
    original_cwd = os.getcwd()
    try:
        dist_dir = dist_data_path.parent
        os.chdir(dist_dir)
        
        # Mock sys.frozen and sys.executable
        import sys
        original_frozen = getattr(sys, 'frozen', False)
        original_executable = getattr(sys, 'executable', '')
        
        sys.frozen = True
        sys.executable = str(dist_dir / "Writing Tools.exe")
        
        settings_manager = SettingsManager(mode="build-final")
        settings_manager.load_settings()
        
        has_providers = settings_manager.has_providers_configured()
        print(f"Real dist data - has_providers_configured(): {has_providers}")
        print(f"provider: '{settings_manager.settings.system.provider}'")
        print(f"api_key: '{settings_manager.settings.system.api_key}'")
        print(f"custom_data: {settings_manager.settings.custom_data}")
        
        if has_providers:
            print("❌ Should show onboarding but won't (has_providers=True)")
        else:
            print("✓ Will show onboarding correctly (has_providers=False)")
        
        # Restore original values
        sys.frozen = original_frozen
        sys.executable = original_executable
        
    finally:
        os.chdir(original_cwd)


def main():
    """Run all tests"""
    print("Testing onboarding logic...")
    
    try:
        test_has_providers_configured()
        test_real_dist_data()
        
        print("\n🎉 All tests completed!")
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

#!/usr/bin/env python3
"""
Test script for error handling improvements
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(__file__))

from PySide6.QtWidgets import QApplication
from WritingToolApp import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_api_errors():
    """Test different API error scenarios"""
    
    # Create a minimal Qt application
    app = WritingToolApp([])
    
    print("Testing API error handling...")
    
    # Test 1: Invalid API Key
    print("\n1. Testing Invalid API Key error...")
    app.show_message_signal.emit(
        "Invalid API Key",
        "Your Gemini API key is invalid. Please check your API key in Settings and make sure it's correct."
    )
    
    # Test 2: Quota Exceeded
    print("2. Testing Quota Exceeded error...")
    app.show_message_signal.emit(
        "Quota Exceeded", 
        "You've exceeded your Gemini API quota. Please check your usage limits or try again later."
    )
    
    # Test 3: Connection Error
    print("3. Testing Connection Error...")
    app.show_message_signal.emit(
        "Connection Error",
        "Cannot connect to Ollama server. Please make sure Ollama is running and check your server URL in Settings."
    )
    
    # Test 4: Generic Error (should not have settings button)
    print("4. Testing Generic Error...")
    app.show_message_signal.emit(
        "Error",
        "This is a generic error message that should not have a settings button."
    )
    
    print("\nError handling test completed!")
    print("Each message box should:")
    print("- Show appropriate title and message")
    print("- Have 'Open Settings' button for API-related errors")
    print("- Only have 'OK' button for generic errors")
    
    return app

if __name__ == "__main__":
    app = test_api_errors()
    print("\nPress Ctrl+C to exit...")
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(0)

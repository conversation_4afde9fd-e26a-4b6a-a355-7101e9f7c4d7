#!/usr/bin/env python3
"""
Writing Tools - New Config System Test
Tests the new config/config_sauv system
"""

import json
import logging
import os
import sys
import tempfile
import shutil
from pathlib import Path

# Enable debug logging
logging.basicConfig(level=logging.DEBUG, format="%(levelname)s: %(message)s")

# Add the parent directory to the path to import config modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import SettingsManager


class TestNewConfig:
    """Test suite for the new config system"""

    def __init__(self):
        self.test_dir = None
        self.original_cwd = os.getcwd()
        self.passed_tests = 0
        self.total_tests = 0

    def setup_test_environment(self):
        """Create a temporary test environment"""
        self.test_dir = tempfile.mkdtemp(prefix="writing_tools_config_test_")
        os.chdir(self.test_dir)

        # Create config structure
        os.makedirs("config/config_sauv", exist_ok=True)
        os.makedirs("config/backgrounds", exist_ok=True)

        # Create default_data.json
        default_data = {
            "system": {
                "api_key": "",
                "provider": "Gemini",
                "model": "gemini-2.0-flash",
                "hotkey": "ctrl+space",
            },
            "actions": {
                "Proofread": {
                    "prefix": "Proofread this:\n\n",
                    "instruction": "Test instruction",
                    "icon": "icons/test",
                    "open_in_window": False,
                }
            },
            "custom_data": {},
        }

        with open("config/default_data.json", "w") as f:
            json.dump(default_data, f, indent=2)

        print(f"Test environment: {self.test_dir}")

    def cleanup_test_environment(self):
        """Clean up the test environment"""
        os.chdir(self.original_cwd)
        if self.test_dir and os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def assert_test(self, condition, test_name, error_msg=""):
        """Assert a test condition and track results"""
        self.total_tests += 1
        if condition:
            print(f"✓ {test_name}")
            self.passed_tests += 1
        else:
            print(f"✗ {test_name}: {error_msg}")

    def test_dev_mode(self):
        """Test dev mode behavior"""
        print("\n--- Test: Dev Mode ---")

        settings_manager = SettingsManager(mode="dev")
        settings = settings_manager.load_settings()

        self.assert_test(
            settings.system.provider == "Gemini", "Default provider loaded in dev mode"
        )

        # Test saving
        success = settings_manager.update_system_setting("api_key", "dev_test_key")

        self.assert_test(success, "Settings update successful in dev mode")

        # Check that file was created in config_sauv
        expected_file = str(settings_manager.data_file)
        self.assert_test(
            os.path.exists(expected_file),
            f"Settings saved to {expected_file} in dev mode",
        )

        if os.path.exists(expected_file):
            # Verify content
            with open(expected_file, "r") as f:
                saved_data = json.load(f)

            self.assert_test(
                saved_data["system"]["api_key"] == "dev_test_key",
                "API key saved correctly in dev mode",
            )

    def test_build_final_mode(self):
        """Test build-final mode behavior"""
        print("\n--- Test: Build-Final Mode ---")

        # Create data.json in root (simulating final build)
        root_data = {
            "system": {
                "api_key": "",
                "provider": "Gemini",
                "model": "gemini-2.0-flash",
            },
            "actions": {},
            "custom_data": {},
        }

        with open("data.json", "w") as f:
            json.dump(root_data, f)

        settings_manager = SettingsManager(mode="build-final")
        settings = settings_manager.load_settings()

        self.assert_test(
            settings.system.provider == "Gemini",
            "Settings loaded from root data.json in build-final mode",
        )

        # Test that data file is in root
        self.assert_test(
            str(settings_manager.data_file).endswith("data.json"),
            "Data file is in root for build-final mode",
        )

    def test_build_dev_mode(self):
        """Test build-dev mode behavior"""
        print("\n--- Test: Build-Dev Mode ---")

        # Clean up any existing files first
        if os.path.exists("config/config_sauv/data.json"):
            os.remove("config/config_sauv/data.json")

        # Create config_sauv/data.json (simulating build-dev)
        os.makedirs("config/config_sauv", exist_ok=True)
        build_dev_data = {
            "system": {"api_key": "build_dev_key", "provider": "OpenAI"},
            "actions": {},
            "custom_data": {},
        }

        with open("config/config_sauv/data.json", "w") as f:
            json.dump(build_dev_data, f)

        settings_manager = SettingsManager(mode="build-dev")
        settings = settings_manager.load_settings()

        self.assert_test(
            settings.system.api_key == "build_dev_key",
            f"Settings loaded from config_sauv in build-dev mode (got: '{settings.system.api_key}')",
        )

        self.assert_test(
            settings.system.provider == "OpenAI",
            f"Provider loaded correctly in build-dev mode (got: '{settings.system.provider}')",
        )

    def test_default_fallback(self):
        """Test fallback to default_data.json"""
        print("\n--- Test: Default Fallback ---")

        # Remove any existing data files
        for f in ["data.json", "config/config_sauv/data.json"]:
            if os.path.exists(f):
                os.remove(f)

        settings_manager = SettingsManager(mode="dev")
        settings = settings_manager.load_settings()

        self.assert_test(
            settings.system.provider == "Gemini", "Fallback to default_data.json works"
        )

        self.assert_test(
            "Proofread" in settings.actions,
            "Default actions loaded from default_data.json",
        )

    def cleanup_between_tests(self):
        """Clean up files between tests"""
        files_to_clean = ["data.json", "config/config_sauv/data.json"]
        for file_path in files_to_clean:
            if os.path.exists(file_path):
                os.remove(file_path)

    def run_all_tests(self):
        """Run all tests"""
        print("=== Writing Tools New Config System Test ===")

        try:
            self.setup_test_environment()

            self.test_dev_mode()
            self.cleanup_between_tests()

            self.test_build_final_mode()
            self.cleanup_between_tests()

            self.test_build_dev_mode()
            self.cleanup_between_tests()

            self.test_default_fallback()

            print(f"\n=== Test Results ===")
            print(f"Passed: {self.passed_tests}/{self.total_tests}")

            if self.passed_tests == self.total_tests:
                print("✓ All tests passed!")
                return True
            else:
                print("✗ Some tests failed!")
                return False

        finally:
            self.cleanup_test_environment()


def main():
    """Main entry point"""
    test_suite = TestNewConfig()
    success = test_suite.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

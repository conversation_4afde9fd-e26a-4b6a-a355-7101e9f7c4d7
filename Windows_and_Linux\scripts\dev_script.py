#!/usr/bin/env python3
"""
Writing Tools - Development Launcher
Cross-platform development environment setup and launcher
"""

import os
import sys
import subprocess

try:
    from .utils import (
        get_project_root,
        setup_environment,
        terminate_existing_processes,
        get_activation_script,
        get_executable_name,
    )
except ImportError:
    from utils import (
        get_project_root,
        setup_environment,
        terminate_existing_processes,
        get_activation_script,
        get_executable_name,
    )


def setup_dev_settings():
    """Setup settings for dev mode using new config/config_sauv logic"""
    print("Setting up development settings...")

    # In dev mode, the application will use config_sauv/data.json
    # No need to copy files - the SettingsManager handles this automatically
    config_sauv_dir = "config/config_sauv"
    config_file = f"{config_sauv_dir}/data.json"

    if os.path.exists(config_file):
        print(f"Using existing settings from: {config_file}")
        # Ensure run_mode is set correctly for dev mode
        try:
            import json

            with open(config_file, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Ensure system section exists and set run_mode
            if "system" not in data:
                data["system"] = {}
            data["system"]["run_mode"] = "dev"

            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            print("Updated run_mode to 'dev' in configuration")
        except Exception as e:
            print(f"Warning: Could not update run_mode: {e}")
    else:
        print(
            "No existing settings found. Application will create settings on first run."
        )
        print("Settings will be saved to: config/config_sauv/data.json")


def launch_application(venv_path, script_name="main.py"):
    """Launch the main application using the virtual environment"""
    python_cmd = get_activation_script(venv_path)

    if not os.path.exists(python_cmd):
        print(f"Error: Python executable not found at {python_cmd}")
        return False

    # main.py should be in the current directory (Windows_and_Linux)
    if not os.path.exists(script_name):
        print(f"Error: Main script not found: {script_name}")
        return False

    print(f"Launching {script_name}...")
    try:
        # Launch the application
        subprocess.run([python_cmd, script_name], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: Failed to launch application: {e}")
        return False
    except KeyboardInterrupt:
        print("\nApplication interrupted by user.")
        return True


def clear_console():
    """Clear console screen (cross-platform)"""
    import os

    os.system("cls" if os.name == "nt" else "clear")


def main():
    """Main function"""
    clear_console()
    print("===== Writing Tools - Development Launcher =====")
    print()

    try:
        # Setup project root
        get_project_root()

        # Setup environment (virtual env + dependencies)
        print("Setting up development environment...")
        success, _ = setup_environment()
        if not success:
            print("\nFailed to setup environment!")

            return 1

        # Stop existing processes (both exe and script)
        terminate_existing_processes(
            exe_name=get_executable_name(), script_name="main.py"
        )

        # Setup development settings
        setup_dev_settings()

        # Launch application
        print()
        if not launch_application("myvenv"):
            print("\nFailed to launch application!")

            return 1

        print("\n===== Application finished =====")
        return 0

    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        return 1
    except Exception as e:
        print(f"\nUnexpected error: {e}")

        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
